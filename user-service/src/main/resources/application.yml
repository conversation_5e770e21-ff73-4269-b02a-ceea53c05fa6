# 服务端口
server:
  port: 8081

# 服务名称（Nacos注册名，需与网关路由配置一致）
spring:
  application:
    name: user-service
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: root
  # Nacos服务发现配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password: # 无密码留空
      database: 0
      timeout: 2000

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml # Mapper XML路径
  type-aliases-package: com.dangshi.exam.user.entity # 实体类别名包
  configuration:
    map-underscore-to-camel-case: true # 下划线转驼峰
    log-impl: org.apache.ibatis.logging.stdout.StdOutImpl # 开发环境打印SQL日志

# JWT配置（与网关、其他微服务一致）
dangshi:
  exam:
    jwt:
      secret: dangshiExamSecretKey2024!@#
      expiration: 86400000 # 24小时