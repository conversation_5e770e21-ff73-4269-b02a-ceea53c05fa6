<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dangshi.exam.user.mapper.UserMapper">

    <!-- 根据用户名查询用户 -->
    <select id="selectByUsername" parameterType="String" resultType="com.dangshi.exam.user.entity.User">
        SELECT
        user_id AS userId,
        username,
        password,
        nickname,
        user_type AS userType,
        register_time AS registerTime,
        last_login_time AS lastLoginTime
        FROM t_user
        WHERE username = #{username}
    </select>

    <!-- 根据用户ID查询用户 -->
    <select id="selectById" parameterType="Long" resultType="com.dangshi.exam.user.entity.User">
        SELECT
        user_id AS userId,
        username,
        nickname,
        user_type AS userType,
        register_time AS registerTime,
        last_login_time AS lastLoginTime
        FROM t_user
        WHERE user_id = #{userId}
    </select>

</mapper>