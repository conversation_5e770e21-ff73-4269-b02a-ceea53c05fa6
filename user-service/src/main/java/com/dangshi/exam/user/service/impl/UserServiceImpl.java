package com.dangshi.exam.user.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.common.security.JwtUtils;
import com.dangshi.exam.common.utils.RedisUtils;
import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.mapper.UserMapper;
import com.dangshi.exam.user.service.UserService;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.concurrent.TimeUnit;

/**
 * 用户服务实现
 */
@Service
public class UserServiceImpl extends ServiceImpl<UserMapper, User> implements UserService {

    @Resource
    private UserMapper userMapper;

    @Resource
    private PasswordEncoder passwordEncoder;

    @Resource
    private AuthenticationManager authenticationManager;

    @Resource
    private UserDetailsServiceImpl userDetailsService;

    @Resource
    private JwtUtils jwtUtils;

    @Resource
    private RedisUtils redisUtils;

    /**
     * 用户注册
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> register(User user) {
        // 1. 校验用户名唯一性
        User existUser = userMapper.selectByUsername(user.getUsername());
        if (existUser != null) {
            throw new BusinessException("用户名已存在");
        }

        // 2. 密码加密（BCrypt）
        user.setPassword(passwordEncoder.encode(user.getPassword()));

        // 3. 设置默认值
        user.setUserType(0); // 默认普通用户
        user.setRegisterTime(LocalDateTime.now());

        // 4. 保存用户
        int rows = userMapper.insert(user);
        if (rows <= 0) {
            throw new BusinessException("注册失败，请重试");
        }

        return R.success();
    }

    /**
     * 用户登录
     */
    @Override
    public R<String> login(String username, String password) {
        // 1. Spring Security认证（校验用户名+密码）
        Authentication authentication;
        try {
            authentication = authenticationManager.authenticate(
                    new UsernamePasswordAuthenticationToken(username, password)
            );
        } catch (Exception e) {
            throw new BusinessException("用户名或密码错误");
        }

        // 2. 认证通过，设置安全上下文
        SecurityContextHolder.getContext().setAuthentication(authentication);

        // 3. 生成JWT Token
        UserDetails userDetails = userDetailsService.loadUserByUsername(username);
        String token = jwtUtils.generateToken(userDetails);

        // 4. 更新最后登录时间
        User user = userMapper.selectByUsername(username);
        updateLastLoginTime(user.getUserId());

        return R.success(token);
    }

    /**
     * 获取用户信息（根据用户名）
     */
    @Override
    public R<User> getUserByUsername(String username) {
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        // 隐藏密码，避免泄露
        user.setPassword(null);
        return R.success(user);
    }

    /**
     * 获取用户信息（根据用户ID，供Feign调用）
     */
    @Override
    public User getUserById(Long userId) {
        User user = userMapper.selectById(userId);
        if (user == null) {
            throw new BusinessException("用户不存在");
        }
        // 隐藏密码
        user.setPassword(null);
        return user;
    }

    /**
     * 更新用户信息
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> updateUser(User user) {
        if (user.getUserId() == null) {
            throw new BusinessException("用户ID不能为空");
        }

        // 1. 校验用户是否存在
        User existUser = userMapper.selectById(user.getUserId());
        if (existUser == null) {
            throw new BusinessException("用户不存在");
        }

        // 2. 禁止修改用户名和密码（如需修改需单独接口）
        user.setUsername(null);
        user.setPassword(null);

        // 3. 更新用户信息
        int rows = userMapper.updateById(user);
        if (rows <= 0) {
            throw new BusinessException("更新失败，请重试");
        }

        return R.success();
    }

    /**
     * 更新最后登录时间
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void updateLastLoginTime(Long userId) {
        User user = new User();
        user.setUserId(userId);
        user.setLastLoginTime(LocalDateTime.now());
        userMapper.updateById(user);
    }

    /**
     * 用户退出登录（Token加入黑名单）
     */
    @Override
    public R<?> logout(String token) {
        if (token == null || !token.startsWith("Bearer ")) {
            throw new BusinessException("Token格式错误");
        }

        // 1. 提取Token（去掉"Bearer "前缀）
        token = token.substring(7);

        // 2. 计算Token剩余有效期（设置黑名单有效期与Token剩余时间一致）
        long expireTime = jwtUtils.extractExpiration(token).getTime() - System.currentTimeMillis();
        if (expireTime <= 0) {
            return R.success("Token已过期");
        }

        // 3. 将Token加入Redis黑名单
        String blacklistKey = "token:blacklist:" + token;
        redisUtils.set(blacklistKey, "invalid", expireTime, TimeUnit.MILLISECONDS);

        return R.success();
    }

    /**
     * 管理员查询所有用户（仅管理员）
     */
    @Override
    public R<?> getUserList() {
        // 查询所有用户，隐藏密码
        QueryWrapper<User> queryWrapper = new QueryWrapper<>();
        queryWrapper.select("user_id", "username", "email", "user_type", "register_time", "last_login_time");
        return R.success(userMapper.selectList(queryWrapper));
    }
}