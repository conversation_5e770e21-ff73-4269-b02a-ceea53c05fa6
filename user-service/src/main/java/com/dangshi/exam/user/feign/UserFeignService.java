package com.dangshi.exam.user.feign;

import com.dangshi.exam.common.result.R;
import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.feign.fallback.UserFeignFallback;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 用户Feign接口（供其他微服务调用，如exam-service）
 * fallback：服务降级处理类
 */
@FeignClient(value = "user-service", fallback = UserFeignFallback.class)
public interface UserFeignService {

    /**
     * 根据用户ID查询用户信息（供考试服务调用）
     */
    @GetMapping("/feign/user/{userId}")
    User getUserById(@PathVariable("userId") Long userId);
}