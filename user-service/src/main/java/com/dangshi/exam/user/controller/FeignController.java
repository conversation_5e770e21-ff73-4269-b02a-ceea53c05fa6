package com.dangshi.exam.user.controller;

import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.service.UserService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * Feign接口实现（供其他微服务调用，路径以/feign开头）
 */
@RestController
@RequestMapping("/feign/user")
public class FeignController {

    @Resource
    private UserService userService;

    /**
     * 根据用户ID查询用户（供Feign调用）
     */
    @GetMapping("/{userId}")
    public User getUserById(@PathVariable("userId") Long userId) {
        return userService.getUserById(userId);
    }
}