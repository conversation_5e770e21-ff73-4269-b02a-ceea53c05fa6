package com.dangshi.exam.user.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangshi.exam.user.entity.User;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 用户数据访问接口（继承BaseMapper获得基础CRUD）
 */
@Mapper
public interface UserMapper extends BaseMapper<User> {
    /**
     * 根据用户名查询用户（用于登录认证）
     */
    User selectByUsername(@Param("username") String username);

    /**
     * 根据用户ID查询用户（用于Feign调用）
     */
    User selectById(@Param("userId") Long userId);
}