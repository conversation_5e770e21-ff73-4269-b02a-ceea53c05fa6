package com.dangshi.exam.user.service.impl;

import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.mapper.UserMapper;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.authority.SimpleGrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.security.core.userdetails.UsernameNotFoundException;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * Spring Security用户详情服务（加载用户信息用于认证）
 */
@Service
public class UserDetailsServiceImpl implements UserDetailsService {

    @Resource
    private UserMapper userMapper;

    @Override
    public UserDetails loadUserByUsername(String username) throws UsernameNotFoundException {
        // 1. 根据用户名查询用户
        User user = userMapper.selectByUsername(username);
        if (user == null) {
            throw new BusinessException("用户名不存在");
        }

        // 2. 设置用户角色（ROLE_前缀是Spring Security规范）
        List<GrantedAuthority> authorities = new ArrayList<>();
        if (user.getUserType() == 1) {
            authorities.add(new SimpleGrantedAuthority("ROLE_ADMIN")); // 管理员角色
        } else {
            authorities.add(new SimpleGrantedAuthority("ROLE_USER"));  // 普通用户角色
        }

        // 3. 返回Spring Security的User对象（包含用户名、密码、权限）
        return new org.springframework.security.core.userdetails.User(
                user.getUsername(),
                user.getPassword(),
                authorities
        );
    }
}