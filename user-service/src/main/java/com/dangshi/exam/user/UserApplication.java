package com.dangshi.exam.user;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 用户微服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.dangshi.exam.user", "com.dangshi.exam.common"}) // 扫描公共模块+当前模块
@MapperScan("com.dangshi.exam.user.mapper") // 扫描Mapper接口
@EnableDiscoveryClient // 启用Nacos服务发现
@EnableFeignClients(basePackages = "com.dangshi.exam.user.feign") // 启用Feign客户端
public class UserApplication {
    public static void main(String[] args) {
        SpringApplication.run(UserApplication.class, args);
    }
}