package com.dangshi.exam.user.controller;

import com.dangshi.exam.common.result.R;
import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.service.UserService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 用户API接口（供前端通过网关调用）
 */
@RestController
@RequestMapping("/api/user")
public class UserController {

    @Resource
    private UserService userService;

    /**
     * 用户注册
     */
    @PostMapping("/register")
    public R<?> register(@RequestBody User user) {
        return userService.register(user);
    }

    /**
     * 用户登录
     */
    @PostMapping("/login")
    public R<String> login(
            @RequestParam String username,
            @RequestParam String password
    ) {
        return userService.login(username, password);
    }

    /**
     * 获取当前登录用户信息
     */
    @GetMapping("/info")
    public R<User> getUserInfo(Authentication authentication) {
        // Authentication由Spring Security自动注入，包含当前登录用户信息
        String username = authentication.getName();
        return userService.getUserByUsername(username);
    }

    /**
     * 更新用户信息（本人或管理员）
     */
    @PutMapping("/update")
    public R<?> updateUser(@RequestBody User user, Authentication authentication) {
        String currentUsername = authentication.getName();
        User currentUser = userService.getUserByUsername(currentUsername).getData();

        // 权限校验：普通用户只能修改自己，管理员可修改任意用户
        if (currentUser.getUserType() != 1 && !currentUser.getUserId().equals(user.getUserId())) {
            return R.error(R.FORBIDDEN, "无权限修改该用户信息");
        }

        return userService.updateUser(user);
    }

    /**
     * 用户退出登录
     */
    @PostMapping("/logout")
    public R<?> logout(@RequestHeader("Authorization") String token) {
        return userService.logout(token);
    }

    /**
     * 管理员查询所有用户（仅管理员）
     */
    @GetMapping("/list")
    @PreAuthorize("hasRole('ADMIN')") // 方法级权限控制
    public R<?> getUserList() {
        return R.success(userService.list());
    }
}