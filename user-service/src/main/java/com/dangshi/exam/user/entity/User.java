package com.dangshi.exam.user.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户实体（映射t_user表）
 */
@Data
@TableName("t_user")
public class User {
    // 主键（自增）
    @TableId(type = IdType.AUTO)
    private Long userId;

    // 登录用户名（唯一）
    private String username;

    // 密码（BCrypt加密存储）
    private String password;

    // 昵称
    private String nickname;

    // 用户类型：0-普通用户，1-管理员
    private Integer userType;

    // 注册时间
    private LocalDateTime registerTime;

    // 最后登录时间
    private LocalDateTime lastLoginTime;
}