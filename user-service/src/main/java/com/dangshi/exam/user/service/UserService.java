package com.dangshi.exam.user.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.user.entity.User;

/**
 * 用户服务接口
 */
public interface UserService extends IService<User> {
    /**
     * 用户注册
     */
    R<?> register(User user);

    /**
     * 用户登录（返回JWT Token）
     */
    R<String> login(String username, String password);

    /**
     * 获取用户信息（根据用户名）
     */
    R<User> getUserByUsername(String username);

    /**
     * 获取用户信息（根据用户ID，供Feign调用）
     */
    User getUserById(Long userId);

    /**
     * 更新用户信息
     */
    R<?> updateUser(User user);

    /**
     * 更新最后登录时间
     */
    void updateLastLoginTime(Long userId);

    /**
     * 用户退出登录（将Token加入黑名单）
     */
    R<?> logout(String token);

    /**
     * 管理员查询所有用户（仅管理员）
     */
    R<?> getUserList();
}