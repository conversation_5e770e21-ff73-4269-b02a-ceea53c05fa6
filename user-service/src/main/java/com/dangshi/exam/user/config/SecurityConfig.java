package com.dangshi.exam.user.config;

import com.dangshi.exam.common.security.JwtUtils;
import com.dangshi.exam.user.service.impl.UserDetailsServiceImpl;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.dao.DaoAuthenticationProvider;
import org.springframework.security.config.annotation.authentication.configuration.AuthenticationConfiguration;
import org.springframework.security.config.annotation.method.configuration.EnableMethodSecurity;
import org.springframework.security.config.annotation.web.builders.HttpSecurity;
import org.springframework.security.config.annotation.web.configuration.EnableWebSecurity;
import org.springframework.security.config.http.SessionCreationPolicy;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;
import org.springframework.security.crypto.password.PasswordEncoder;
import org.springframework.security.web.SecurityFilterChain;

import javax.annotation.Resource;

/**
 * 用户微服务安全配置（密码加密、认证管理器、接口权限）
 */
@Configuration
@EnableWebSecurity
@EnableMethodSecurity // 启用方法级权限控制（@PreAuthorize）
public class SecurityConfig {

    @Resource
    private UserDetailsServiceImpl userDetailsService;

    @Resource
    private JwtUtils jwtUtils;

    /**
     * 密码加密器（BCrypt加密，不可逆）
     */
    @Bean
    public PasswordEncoder passwordEncoder() {
        return new BCryptPasswordEncoder();
    }

    /**
     * 认证管理器（用于用户登录认证）
     */
    @Bean
    public AuthenticationManager authenticationManager(AuthenticationConfiguration authConfig) throws Exception {
        return authConfig.getAuthenticationManager();
    }

    /**
     * 认证提供者（关联用户详情服务和密码加密器）
     */
    @Bean
    public DaoAuthenticationProvider authenticationProvider() {
        DaoAuthenticationProvider authProvider = new DaoAuthenticationProvider();
        authProvider.setUserDetailsService(userDetailsService);
        authProvider.setPasswordEncoder(passwordEncoder());
        return authProvider;
    }

    /**
     * 安全过滤器链（配置接口权限、会话策略）
     */
    @Bean
    public SecurityFilterChain filterChain(HttpSecurity http) throws Exception {
        http
                .cors().and().csrf().disable() // 关闭CSRF，适配前后端分离
                .sessionManagement().sessionCreationPolicy(SessionCreationPolicy.STATELESS).and() // 无状态会话（不依赖Session）
                .authorizeHttpRequests(auth -> auth
                        // 允许匿名访问的接口（登录、注册）
                        .requestMatchers("/api/user/login", "/api/user/register").permitAll()
                        // Feign接口允许内部服务访问（通过网关转发时已校验Token）
                        .requestMatchers("/feign/user/**").permitAll()
                        // 其他接口需认证
                        .anyRequest().authenticated()
                );

        // 关联认证提供者
        http.authenticationProvider(authenticationProvider());

        return http.build();
    }
}