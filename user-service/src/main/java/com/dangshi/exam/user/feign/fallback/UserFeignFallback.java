package com.dangshi.exam.user.feign.fallback;

import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.user.entity.User;
import com.dangshi.exam.user.feign.UserFeignService;
import org.springframework.stereotype.Component;

/**
 * 用户Feign降级处理（当user-service不可用时触发）
 */
@Component
public class UserFeignFallback implements UserFeignService {

    @Override
    public User getUserById(Long userId) {
        // 服务降级：抛出异常或返回默认值
        throw new BusinessException("用户服务暂时不可用，请稍后重试");
    }
}