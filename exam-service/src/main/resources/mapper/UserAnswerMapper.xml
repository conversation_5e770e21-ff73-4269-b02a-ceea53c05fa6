<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dangshi.exam.exam.mapper.UserAnswerMapper">

    <!-- 批量插入用户答案 -->
    <insert id="batchInsert">
        INSERT INTO t_user_answer (exam_id, question_id, user_answer, is_correct, submit_time)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.examId}, #{item.questionId}, #{item.userAnswer}, #{item.isCorrect}, #{item.submitTime})
        </foreach>
    </insert>

    <!-- 根据考试ID查询用户答案 -->
    <select id="selectByExamId" resultType="com.dangshi.exam.exam.entity.UserAnswer">
        SELECT
        id,
        exam_id AS examId,
        question_id AS questionId,
        user_answer AS userAnswer,
        is_correct AS isCorrect,
        submit_time AS submitTime
        FROM t_user_answer
        WHERE exam_id = #{examId}
    </select>

    <!-- 通过考试ID查询试卷ID（关联t_exam表） -->
    <select id="selectPaperIdByExamId" parameterType="String" resultType="java.lang.Long">
        SELECT e.paper_id
        FROM t_exam e
        WHERE e.exam_id = #{examId}
    </select>
</mapper>