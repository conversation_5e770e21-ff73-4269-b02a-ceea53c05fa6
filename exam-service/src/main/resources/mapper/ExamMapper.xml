<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dangshi.exam.exam.mapper.ExamMapper">

    <!-- 分页查询用户考试记录 -->
    <select id="selectUserExamPage" resultType="com.dangshi.exam.exam.entity.Exam">
        SELECT
        e.exam_id AS examId,
        e.user_id AS userId,
        e.paper_id AS paperId,
        e.start_time AS startTime,
        e.end_time AS endTime,
        e.status AS status,
        e.score AS score
        FROM t_exam e
        WHERE e.user_id = #{userId}
        ORDER BY e.start_time DESC
    </select>

    <!-- 查询考试信息（含试卷名称） -->
    <select id="selectExamWithPaperName" resultType="com.dangshi.exam.exam.entity.Exam">
        SELECT
        e.exam_id AS examId,
        e.user_id AS userId,
        e.paper_id AS paperId,
        p.paper_name AS paperName,
        e.start_time AS startTime,
        e.end_time AS endTime,
        e.status AS status,
        e.score AS score
        FROM t_exam e
        LEFT JOIN t_paper p ON e.paper_id = p.paper_id
        WHERE e.exam_id = #{examId}
    </select>

</mapper>