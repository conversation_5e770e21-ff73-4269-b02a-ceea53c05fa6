# 服务端口
server:
  port: 8083

# 服务名称
spring:
  application:
    name: exam-service
  # 数据库配置
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    url: ************************************************************************************************************
    username: root
    password: root
  # Nacos配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848
        namespace: public
  # Redis配置
  data:
    redis:
      host: localhost
      port: 6379
      password:
      database: 0
      timeout: 2000

# MyBatis-Plus配置
mybatis-plus:
  mapper-locations: classpath:mapper/**/*.xml
  type-aliases-package: com.dangshi.exam.exam.entity
  configuration:
    map-underscore-to-camel-case: true