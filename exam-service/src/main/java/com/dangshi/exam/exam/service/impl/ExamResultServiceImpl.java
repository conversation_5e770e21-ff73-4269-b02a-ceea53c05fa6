package com.dangshi.exam.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.exam.entity.Exam;
import com.dangshi.exam.exam.entity.ExamResult;
import com.dangshi.exam.exam.entity.UserAnswer;
import com.dangshi.exam.exam.mapper.ExamMapper;
import com.dangshi.exam.exam.mapper.ExamResultMapper;
import com.dangshi.exam.exam.service.ExamResultService;
import com.dangshi.exam.exam.service.UserAnswerService;
import com.dangshi.exam.exam.vo.ExamResultStatVO;
import com.dangshi.exam.exam.vo.ExamResultVO;
import com.dangshi.exam.exam.feign.PaperFeignService;
import com.dangshi.exam.common.vo.PaperVO;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 考试结果服务实现类
 */
@Service
public class ExamResultServiceImpl extends ServiceImpl<ExamResultMapper, ExamResult> implements ExamResultService {

    @Resource
    private ExamResultMapper examResultMapper;

    @Resource
    private ExamMapper examMapper;

    @Resource
    private UserAnswerService userAnswerService;

    @Resource
    private PaperFeignService paperFeignService;

    /**
     * 根据考试ID查询考试结果
     */
    @Override
    public ExamResult getByExamId(String examId) {
        if (examId == null || examId.trim().isEmpty()) {
            throw new BusinessException("考试ID不能为空");
        }
        QueryWrapper<ExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_id", examId);
        return examResultMapper.selectOne(queryWrapper);
    }

    /**
     * 根据用户ID分页查询考试结果
     */
    @Override
    public IPage<ExamResult> getUserExamResults(Long userId, Integer pageNum, Integer pageSize) {
        if (userId == null) {
            throw new BusinessException("用户ID不能为空");
        }
        Page<ExamResult> page = new Page<>(pageNum, pageSize);
        QueryWrapper<ExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("user_id", userId)
                   .orderByDesc("create_time"); // 按考试时间倒序
        return examResultMapper.selectPage(page, queryWrapper);
    }

    /**
     * 根据试卷ID统计考试结果
     */
    @Override
    public ExamResultStatVO statByPaperId(Long paperId) {
        if (paperId == null) {
            throw new BusinessException("试卷ID不能为空");
        }

        // 查询该试卷的所有考试结果
        QueryWrapper<ExamResult> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("paper_id", paperId);
        List<ExamResult> results = examResultMapper.selectList(queryWrapper);
        if (results.isEmpty()) {
            return new ExamResultStatVO(); // 返回空统计
        }

        // 计算统计指标
        int totalExam = results.size(); // 总考试次数
        int passCount = 0; // 通过次数（假设60分为及格线）
        BigDecimal totalScore = BigDecimal.ZERO; // 总分之和

        for (ExamResult result : results) {
            totalScore = totalScore.add(BigDecimal.valueOf(result.getUserScore()));
            if (result.getUserScore() >= 60) {
                passCount++;
            }
        }

        // 计算平均分（保留1位小数）
        BigDecimal avgScore = totalScore.divide(
                BigDecimal.valueOf(totalExam),
                1,
                RoundingMode.HALF_UP
        );

        // 计算通过率（保留1位小数）
        BigDecimal passRate = BigDecimal.valueOf(passCount)
                .divide(BigDecimal.valueOf(totalExam), 2, RoundingMode.HALF_UP)
                .multiply(BigDecimal.valueOf(100));

        // 封装统计结果
        ExamResultStatVO statVO = new ExamResultStatVO();
        statVO.setPaperId(paperId);
        statVO.setTotalExam(totalExam);
        statVO.setAvgScore(avgScore.doubleValue());
        statVO.setPassRate(passRate.doubleValue());
        statVO.setPassCount(passCount);
        statVO.setFailCount(totalExam - passCount);
        return statVO;
    }

    /**
     * 转换考试结果实体为VO（包含详细信息）
     */
    @Override
    public ExamResultVO convertToVO(ExamResult examResult) {
        if (examResult == null) {
            return null;
        }

        // 查询关联的考试记录
        Exam exam = examMapper.selectById(examResult.getExamId());
        if (exam == null) {
            throw new BusinessException("考试记录不存在：" + examResult.getExamId());
        }

        // 查询关联的试卷信息
        PaperVO paperVO = paperFeignService.getPaperDetail(examResult.getPaperId());

        // 查询用户答案列表
        List<UserAnswer> userAnswers = userAnswerService.getByExamId(examResult.getExamId());

        // 计算考试时长（分钟）
        LocalDateTime startTime = exam.getStartTime();
        LocalDateTime endTime = exam.getEndTime();
        int timeUsed = endTime != null ? (int) Duration.between(startTime, endTime).toMinutes() : 0;

        // 封装VO
        ExamResultVO vo = new ExamResultVO();
        vo.setExamId(examResult.getExamId());
        vo.setPaperName(paperVO.getPaperName());
        vo.setTotalScore(examResult.getTotalScore());
        vo.setUserScore(examResult.getUserScore());
        vo.setCorrectCount(examResult.getCorrectCount());
        vo.setWrongCount(examResult.getWrongCount());
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        vo.setTimeUsed(timeUsed);



        // 筛选错题列表
        List<ExamResultVO.UserAnswerDetailVO> wrongAnswers = vo.getUserAnswers().stream()
                .filter(detail -> detail.getIsCorrect() == 0)
                .collect(Collectors.toList());
        vo.setWrongQuestions(wrongAnswers);

        return vo;
    }
}