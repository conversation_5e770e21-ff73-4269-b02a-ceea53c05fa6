package com.dangshi.exam.exam.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.common.utils.RedisUtils;
import com.dangshi.exam.exam.entity.Exam;
import com.dangshi.exam.exam.entity.ExamResult;
import com.dangshi.exam.exam.entity.UserAnswer;
import com.dangshi.exam.exam.mapper.ExamMapper;
import com.dangshi.exam.exam.service.ExamResultService;
import com.dangshi.exam.exam.service.ExamService;
import com.dangshi.exam.exam.service.UserAnswerService;
import com.dangshi.exam.exam.vo.ExamResultVO;
import com.dangshi.exam.exam.vo.ExamSubmitVO;
import com.dangshi.exam.exam.vo.UserAnswerVO;
import com.dangshi.exam.exam.feign.PaperFeignService;
import com.dangshi.exam.question.vo.PaperVO;
import com.dangshi.exam.question.vo.QuestionVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * 考试服务实现
 */
@Service
public abstract class ExamServiceImpl extends ServiceImpl<ExamMapper, Exam> implements ExamService {

    // Redis缓存键前缀（答题进度）
    private static final String EXAM_PROGRESS_KEY = "exam:progress:";

    // 答题进度缓存过期时间（小时）
    private static final int PROGRESS_EXPIRE_HOURS = 24;

    @Resource
    private ExamMapper examMapper;

    @Resource
    private UserAnswerService userAnswerService;

    @Resource
    private ExamResultService examResultService;

    @Resource
    private PaperFeignService paperFeignService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 开始考试
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<String> startExam(Long paperId, Long userId) {
        // 1. 校验试卷是否存在（调用题库服务）
        PaperVO paperVO = paperFeignService.getPaperDetail(paperId);
        if (paperVO == null) {
            throw new BusinessException("试卷不存在");
        }

        // 2. 生成考试ID（格式：EX+时间戳+随机数）
        String timeStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyyMMddHHmmss"));
        String randomStr = UUID.randomUUID().toString().substring(0, 4);
        String examId = "EX" + timeStr + randomStr;

        // 3. 创建考试记录
        Exam exam = new Exam();
        exam.setExamId(examId);
        exam.setUserId(userId);
        exam.setPaperId(paperId);
        exam.setStartTime(LocalDateTime.now());
        exam.setStatus(0); // 0-进行中
        examMapper.insert(exam);

        return R.success(examId);
    }

    /**
     * 提交试卷（自动判分）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<ExamResultVO> submitExam(ExamSubmitVO submitVO) {
        String examId = submitVO.getExamId();
        List<UserAnswerVO> answerVOs = submitVO.getUserAnswers();

        // 1. 校验考试是否存在且未提交
        Exam exam = examMapper.selectById(examId);
        if (exam == null) {
            throw new BusinessException("考试记录不存在");
        }
        if (exam.getStatus() == 1) {
            throw new BusinessException("试卷已提交，无法重复提交");
        }

        // 2. 获取试卷题目（调用题库服务）
        PaperVO paperVO = paperFeignService.getPaperDetail(exam.getPaperId());
        List<QuestionVO> questions = paperVO.getQuestions();

        // 3. 自动判分
        int userScore = 0;
        int correctCount = 0;
        List<UserAnswer> userAnswers = new ArrayList<>();
        List<QuestionVO> wrongQuestions = new ArrayList<>();

        for (UserAnswerVO answerVO : answerVOs) {
            // 匹配题目
            QuestionVO question = questions.stream()
                    .filter(q -> q.getQuestionId().equals(answerVO.getQuestionId()))
                    .findFirst()
                    .orElseThrow(() -> new BusinessException("题目ID不存在：" + answerVO.getQuestionId()));

            // 判断答案是否正确
            boolean isCorrect = judgeAnswer(question, answerVO.getUserAnswer());
            int isCorrectInt = isCorrect ? 1 : 0;

            // 累加得分和正确题数
            if (isCorrect) {
                userScore += question.getScore();
                correctCount++;
            } else {
                wrongQuestions.add(question);
            }

            // 封装用户答案实体
            UserAnswer userAnswer = new UserAnswer();
            userAnswer.setExamId(examId);
            userAnswer.setQuestionId(answerVO.getQuestionId());
            userAnswer.setUserAnswer(answerVO.getUserAnswer());
            userAnswer.setIsCorrect(isCorrectInt);
            userAnswer.setSubmitTime(LocalDateTime.now());
            userAnswers.add(userAnswer);
        }

        // 4. 批量保存用户答案
        userAnswerService.batchSave(userAnswers);

        // 5. 更新考试记录
        exam.setStatus(1); // 1-已提交
        exam.setEndTime(LocalDateTime.now());
        exam.setScore(userScore);
        examMapper.updateById(exam);

        // 6. 生成考试结果
        ExamResult examResult = new ExamResult();
        examResult.setExamId(examId);
        examResult.setUserId(exam.getUserId());
        examResult.setPaperId(exam.getPaperId());
        examResult.setTotalScore(paperVO.getTotalScore());
        examResult.setUserScore(userScore);
        examResult.setCorrectCount(correctCount);
        examResult.setWrongCount(questions.size() - correctCount);
        examResult.setCreateTime(LocalDateTime.now());
        examResultService.save(examResult);

        // 7. 清除答题进度缓存
        redisUtils.delete(EXAM_PROGRESS_KEY + examId);

        // 8. 封装结果VO
        ExamResultVO resultVO = buildExamResultVO(exam, paperVO, userAnswers, wrongQuestions, userScore);
        return R.success(resultVO);
    }

    /**
     * 分页查询用户考试记录
     */
    @Override
    public R<IPage<Exam>> getUserExamRecord(Long userId, Integer pageNum, Integer pageSize) {
        Page<Exam> page = new Page<>(pageNum, pageSize);
        IPage<Exam> examPage = examMapper.selectUserExamPage(page, userId);
        return R.success(examPage);
    }

    /**
     * 查询考试结果详情
     */
    @Override
    public R<ExamResultVO> getExamResult(String examId) {
        // 1. 查询考试信息
        Exam exam = examMapper.selectExamWithPaperName(examId);
        if (exam == null) {
            throw new BusinessException("考试记录不存在");
        }
        if (exam.getStatus() == 0) {
            throw new BusinessException("考试未完成，无法查看结果");
        }

        // 2. 查询试卷题目
        PaperVO paperVO = paperFeignService.getPaperDetail(exam.getPaperId());

        // 3. 查询用户答案
        List<UserAnswer> userAnswers = userAnswerService.getByExamId(examId);

        // 4. 查询错题列表
        List<QuestionVO> wrongQuestions = getWrongQuestions(userAnswers, paperVO.getQuestions());

        // 5. 封装结果VO
        ExamResultVO resultVO = buildExamResultVO(
                exam, paperVO, userAnswers, wrongQuestions, exam.getScore()
        );
        return R.success(resultVO);
    }

    /**
     * 保存答题进度（Redis）
     */
    @Override
    public R<?> saveExamProgress(String examId, ExamSubmitVO submitVO) {
        try {
            // 序列化答题进度
            String progressJson = objectMapper.writeValueAsString(submitVO);
            // 存入Redis（设置过期时间）
            redisUtils.set(
                    EXAM_PROGRESS_KEY + examId,
                    progressJson,
                    PROGRESS_EXPIRE_HOURS,
                    TimeUnit.HOURS
            );
            return R.success();
        } catch (JsonProcessingException e) {
            throw new BusinessException("保存答题进度失败");
        }
    }

    /**
     * 答案判分逻辑
     */
    private boolean judgeAnswer(QuestionVO question, String userAnswer) {
        if (userAnswer == null || userAnswer.trim().isEmpty()) {
            return false;
        }
        String correctAnswer = question.getCorrectAnswer();

        // 多选题：答案按逗号分割后排序比对（避免顺序影响）
        if (question.getQuestionType() == 2) {
            String[] userAnsArr = userAnswer.split(",");
            String[] correctAnsArr = correctAnswer.split(",");
            java.util.Arrays.sort(userAnsArr);
            java.util.Arrays.sort(correctAnsArr);
            return java.util.Arrays.equals(userAnsArr, correctAnsArr);
        }

        // 单选/判断：直接比对
        return correctAnswer.equals(userAnswer);
    }

    /**
     * 提取错题列表
     */
    private List<QuestionVO> getWrongQuestions(List<UserAnswer> userAnswers, List<QuestionVO> questions) {
        List<Long> wrongQuestionIds = userAnswers.stream()
                .filter(answer -> answer.getIsCorrect() == 0)
                .map(UserAnswer::getQuestionId)
                .collect(Collectors.toList());

        return questions.stream()
                .filter(question -> wrongQuestionIds.contains(question.getQuestionId()))
                .collect(Collectors.toList());
    }

    /**
     * 构建考试结果VO
     */
    private ExamResultVO buildExamResultVO(
            Exam exam,
            PaperVO paperVO,
            List<UserAnswer> userAnswers,
            List<QuestionVO> wrongQuestions,
            int userScore
    ) {
        ExamResultVO resultVO = new ExamResultVO();
        resultVO.setExamId(exam.getExamId());
        resultVO.setPaperName(paperVO.getPaperName());
        resultVO.setTotalScore(paperVO.getTotalScore());
        resultVO.setUserScore(userScore);
        resultVO.setCorrectCount((int) userAnswers.stream().filter(a -> a.getIsCorrect() == 1).count());
        resultVO.setWrongCount(wrongQuestions.size());

        // 封装用户答案详情
        List<ExamResultVO.UserAnswerDetailVO> answerDetails = userAnswers.stream().map(answer -> {
            ExamResultVO.UserAnswerDetailVO detail = new ExamResultVO.UserAnswerDetailVO();
            QuestionVO question = paperVO.getQuestions().stream()
                    .filter(q -> q.getQuestionId().equals(answer.getQuestionId()))
                    .findFirst()
                    .orElse(null);
            if (question != null) {
                detail.setQuestionId(question.getQuestionId());
                detail.setQuestionContent(question.getQuestionContent());
                detail.setCorrectAnswer(question.getCorrectAnswer());
            }
            detail.setUserAnswer(answer.getUserAnswer());
            detail.setIsCorrect(answer.getIsCorrect());
            return detail;
        }).collect(Collectors.toList());
        resultVO.setUserAnswers(answerDetails);

        return resultVO;
    }
}