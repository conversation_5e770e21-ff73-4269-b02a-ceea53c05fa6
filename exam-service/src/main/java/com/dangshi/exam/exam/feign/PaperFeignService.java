package com.dangshi.exam.exam.feign;

import com.dangshi.exam.exam.feign.fallback.PaperFeignFallback;
import com.dangshi.exam.common.vo.PaperVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 调用题库服务的Feign接口
 */
@FeignClient(value = "question-service", fallback = PaperFeignFallback.class)
public interface PaperFeignService {

    /**
     * 根据试卷ID查询试卷详情（含题目列表）
     */
    @GetMapping("/feign/paper/{paperId}")
    PaperVO getPaperDetail(@PathVariable("paperId") Long paperId);
}