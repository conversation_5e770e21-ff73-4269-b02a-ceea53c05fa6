package com.dangshi.exam.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试结果表（t_exam_result）
 */
@Data
@TableName("t_exam_result")
public class ExamResult {
    @TableId(type = IdType.AUTO)
    private Long resultId; // 结果ID

    private String examId; // 考试ID（关联t_exam）

    private Long userId; // 用户ID

    private Long paperId; // 试卷ID

    private Integer totalScore; // 试卷总分

    private Integer userScore; // 用户得分

    private Integer correctCount; // 正确题数

    private Integer wrongCount; // 错误题数

    private LocalDateTime createTime; // 结果生成时间
}