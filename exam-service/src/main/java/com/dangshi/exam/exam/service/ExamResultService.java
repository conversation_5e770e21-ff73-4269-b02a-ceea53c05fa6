package com.dangshi.exam.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dangshi.exam.exam.entity.ExamResult;
import com.dangshi.exam.exam.vo.ExamResultStatVO;
import com.dangshi.exam.exam.vo.ExamResultVO;

/**
 * 考试结果服务接口
 * 处理考试成绩的CRUD及统计分析
 */
public interface ExamResultService extends IService<ExamResult> {

    /**
     * 根据考试ID查询考试结果
     * @param examId 考试ID
     * @return 考试结果实体
     */
    ExamResult getByExamId(String examId);

    /**
     * 根据用户ID分页查询考试结果
     * @param userId 用户ID
     * @param pageNum 页码
     * @param pageSize 每页条数
     * @return 分页结果
     */
    IPage<ExamResult> getUserExamResults(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 根据试卷ID统计考试结果（平均分、通过率等）
     * @param paperId 试卷ID
     * @return 统计结果VO
     */
    ExamResultStatVO statByPaperId(Long paperId);

    /**
     * 转换考试结果实体为VO（包含详细信息）
     * @param examResult 考试结果实体
     * @return 转换后的VO
     */
    ExamResultVO convertToVO(ExamResult examResult);
}