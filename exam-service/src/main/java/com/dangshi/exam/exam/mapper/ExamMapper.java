package com.dangshi.exam.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.dangshi.exam.exam.entity.Exam;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考试记录Mapper
 */
@Mapper
public interface ExamMapper extends BaseMapper<Exam> {
    /**
     * 分页查询用户考试记录
     */
    IPage<Exam> selectUserExamPage(Page<Exam> page, @Param("userId") Long userId);

    /**
     * 根据考试ID查询考试信息（含试卷名称）
     */
    Exam selectExamWithPaperName(@Param("examId") String examId);
}