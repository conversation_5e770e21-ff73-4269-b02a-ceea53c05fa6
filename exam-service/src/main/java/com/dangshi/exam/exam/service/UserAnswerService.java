package com.dangshi.exam.exam.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dangshi.exam.exam.entity.UserAnswer;
import com.dangshi.exam.exam.vo.UserAnswerVO;

import java.util.List;

/**
 * 用户答案服务接口
 * 处理用户答题记录的CRUD及批量操作
 */
public interface UserAnswerService extends IService<UserAnswer> {

    /**
     * 批量保存用户答案
     * @param userAnswers 用户答案列表
     */
    void batchSave(List<UserAnswer> userAnswers);

    /**
     * 根据考试ID查询用户答案
     * @param examId 考试ID
     * @return 用户答案列表
     */
    List<UserAnswer> getByExamId(String examId);

    /**
     * 根据考试ID和题目ID查询用户答案
     * @param examId 考试ID
     * @param questionId 题目ID
     * @return 单个用户答案
     */
    UserAnswer getByExamIdAndQuestionId(String examId, Long questionId);

    /**
     * 更新用户答案（用于中途保存进度）
     * @param userAnswer 用户答案实体
     * @return 是否更新成功
     */
    boolean updateUserAnswer(UserAnswer userAnswer);

    /**
     * 批量转换用户答案为VO（包含题目信息）
     *
     * @param userAnswers 用户答案列表
     * @return 转换后的VO列表
     */
    List<UserAnswerVO> convertToVOList(List<UserAnswer> userAnswers);
}