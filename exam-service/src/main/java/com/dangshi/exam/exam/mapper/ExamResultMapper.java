package com.dangshi.exam.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangshi.exam.exam.entity.ExamResult;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

/**
 * 考试结果Mapper
 */
@Mapper
public interface ExamResultMapper extends BaseMapper<ExamResult> {
    /**
     * 根据考试ID查询考试结果
     */
    ExamResult selectByExamId(@Param("examId") String examId);
}