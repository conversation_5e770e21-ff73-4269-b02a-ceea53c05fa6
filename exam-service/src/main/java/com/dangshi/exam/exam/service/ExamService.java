package com.dangshi.exam.exam.service;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.service.IService;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.exam.entity.Exam;
import com.dangshi.exam.exam.vo.ExamResultVO;
import com.dangshi.exam.exam.vo.ExamSubmitVO;

/**
 * 考试服务接口
 */
public interface ExamService extends IService<Exam> {
    /**
     * 开始考试（生成考试ID，创建考试记录）
     */
    R<String> startExam(Long paperId, Long userId);

    /**
     * 提交试卷（自动判分，生成结果）
     */
    R<ExamResultVO> submitExam(ExamSubmitVO submitVO);

    /**
     * 分页查询用户考试记录
     */
    R<IPage<Exam>> getUserExamRecord(Long userId, Integer pageNum, Integer pageSize);

    /**
     * 查询考试结果详情
     */
    R<ExamResultVO> getExamResult(String examId);

    /**
     * 保存答题进度（Redis）
     */
    R<?> saveExamProgress(String examId, ExamSubmitVO submitVO);
}