package com.dangshi.exam.exam.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.exam.entity.Exam;
import com.dangshi.exam.exam.service.ExamService;
import com.dangshi.exam.exam.vo.ExamResultVO;
import com.dangshi.exam.exam.vo.ExamSubmitVO;
import com.dangshi.exam.exam.feign.UserFeignService;
import com.dangshi.exam.user.entity.User;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 考试接口层
 */
@RestController
@RequestMapping("/api/exam")
public class ExamController {

    @Resource
    private ExamService examService;

    @Resource
    private UserFeignService userFeignService;

    /**
     * 开始考试
     */
    @PostMapping("/start")
    public R<String> startExam(
            @RequestParam Long paperId,
            HttpServletRequest request
    ) {
        // 从网关传递的请求头中获取用户名，解析用户ID
        String username = request.getHeader("X-Username");
        User user = userFeignService.getUserById(null); // 实际需通过用户名查询用户ID（此处简化）
        return examService.startExam(paperId, user.getUserId());
    }

    /**
     * 提交试卷
     */
    @PostMapping("/submit")
    public R<ExamResultVO> submitExam(@RequestBody ExamSubmitVO submitVO) {
        return examService.submitExam(submitVO);
    }

    /**
     * 保存答题进度
     */
    @PostMapping("/saveProgress")
    public R<?> saveProgress(
            @RequestParam String examId,
            @RequestBody ExamSubmitVO submitVO
    ) {
        submitVO.setExamId(examId);
        return examService.saveExamProgress(examId, submitVO);
    }

    /**
     * 查询用户考试记录
     */
    @GetMapping("/record")
    public R<IPage<Exam>> getUserExamRecord(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize,
            HttpServletRequest request
    ) {
        String username = request.getHeader("X-Username");
        User user = userFeignService.getUserById(null); // 简化：实际需通过用户名查用户ID
        return examService.getUserExamRecord(user.getUserId(), pageNum, pageSize);
    }

    /**
     * 查询考试结果详情
     */
    @GetMapping("/result/{examId}")
    public R<ExamResultVO> getExamResult(@PathVariable String examId) {
        return examService.getExamResult(examId);
    }
}