package com.dangshi.exam.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 考试记录表（t_exam）
 */
@Data
@TableName("t_exam")
public class Exam {
    @TableId(type = IdType.INPUT) // 考试ID自定义生成，非自增
    private String examId; // 考试ID（格式：EX+时间戳+随机数）

    private Long userId; // 用户ID（关联t_user）

    private Long paperId; // 试卷ID（关联t_paper）

    private LocalDateTime startTime; // 开始时间

    private LocalDateTime endTime; // 结束时间

    private Integer status; // 状态：0-进行中，1-已提交

    private Integer score; // 最终得分
}