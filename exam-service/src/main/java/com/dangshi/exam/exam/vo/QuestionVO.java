package com.dangshi.exam.exam.vo;

import lombok.Data;

/**
 * 题目视图对象（用于前端展示和前后端数据交互）
 */
@Data
public class QuestionVO {
    /**
     * 题目ID
     */
    private Long questionId;

    /**
     * 题型：1-单选，2-多选，3-判断
     */
    private Integer questionType;

    /**
     * 题型文字描述（如"单选"、"多选"）
     */
    private String questionTypeLabel;

    /**
     * 题目内容（题干）
     */
    private String questionContent;

    /**
     * 选项数组（解析后的JSON数组，如["A.选项1","B.选项2"]）
     */
    private String[] options;

    /**
     * 正确答案（单选/判断为单个选项，多选为逗号分隔的选项，如"A,B"）
     */
    private String correctAnswer;

    /**
     * 题目分值
     */
    private Integer score;

    /**
     * 答案解析（可选）
     */
    private String analysis;

    /**
     * 用户答案（仅在考试提交/结果展示时使用）
     */
    private String userAnswer;

    /**
     * 是否正确（仅在考试结果展示时使用：0-错，1-对）
     */
    private Integer isCorrect;

    // 补充题型标签转换方法（可选，用于前端直接展示）
    public void setQuestionType(Integer questionType) {
        this.questionType = questionType;
        // 自动设置题型文字描述
        switch (questionType) {
            case 1:
                this.questionTypeLabel = "单选题";
                break;
            case 2:
                this.questionTypeLabel = "多选题";
                break;
            case 3:
                this.questionTypeLabel = "判断题";
                break;
            default:
                this.questionTypeLabel = "未知题型";
        }
    }
}