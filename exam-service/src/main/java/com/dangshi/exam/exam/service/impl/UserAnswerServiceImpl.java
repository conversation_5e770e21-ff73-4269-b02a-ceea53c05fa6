package com.dangshi.exam.exam.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.exam.entity.UserAnswer;
import com.dangshi.exam.exam.mapper.UserAnswerMapper;
import com.dangshi.exam.exam.service.UserAnswerService;
import com.dangshi.exam.exam.vo.UserAnswerVO;
import com.dangshi.exam.exam.feign.PaperFeignService;
import com.dangshi.exam.common.vo.QuestionVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

import static com.baomidou.mybatisplus.extension.toolkit.Db.save;
import static com.baomidou.mybatisplus.extension.toolkit.Db.updateById;

/**
 * 用户答案服务实现类
 */
@Service
public class UserAnswerServiceImpl extends ServiceImpl<UserAnswerMapper, UserAnswer> implements UserAnswerService {

    @Resource
    private UserAnswerMapper userAnswerMapper;

    @Resource
    private PaperFeignService paperFeignService; // 用于查询题目详情

    /**
     * 批量保存用户答案（支持事务回滚）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public void batchSave(List<UserAnswer> userAnswers) {
        if (userAnswers == null || userAnswers.isEmpty()) {
            throw new BusinessException("用户答案列表不能为空");
        }
        // 批量插入（MyBatis-Plus的saveBatch效率较低，此处使用自定义批量插入SQL）
        int rows = userAnswerMapper.batchInsert(userAnswers);
        if (rows != userAnswers.size()) {
            throw new BusinessException("批量保存答案失败，部分数据未插入");
        }
    }

    /**
     * 根据考试ID查询用户答案
     */
    @Override
    public List<UserAnswer> getByExamId(String examId) {
        if (examId == null || examId.trim().isEmpty()) {
            throw new BusinessException("考试ID不能为空");
        }
        QueryWrapper<UserAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_id", examId)
                   .orderByAsc("question_id"); // 按题目ID排序
        return userAnswerMapper.selectList(queryWrapper);
    }

    /**
     * 根据考试ID和题目ID查询用户答案
     */
    @Override
    public UserAnswer getByExamIdAndQuestionId(String examId, Long questionId) {
        if (examId == null || questionId == null) {
            throw new BusinessException("考试ID和题目ID不能为空");
        }
        QueryWrapper<UserAnswer> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("exam_id", examId)
                   .eq("question_id", questionId);
        return userAnswerMapper.selectOne(queryWrapper);
    }

    /**
     * 更新用户答案（用于中途保存进度）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updateUserAnswer(UserAnswer userAnswer) {
        if (userAnswer.getExamId() == null || userAnswer.getQuestionId() == null) {
            throw new BusinessException("考试ID和题目ID不能为空");
        }
        // 补充更新时间
        userAnswer.setSubmitTime(LocalDateTime.now());
        // 先查询是否存在，存在则更新，不存在则插入
        UserAnswer existing = getByExamIdAndQuestionId(userAnswer.getExamId(), userAnswer.getQuestionId());
        if (existing != null) {
            userAnswer.setId(existing.getId()); // 复用ID
            return updateById(userAnswer);
        } else {
            return save(userAnswer);
        }
    }

    /**
     * 批量转换用户答案为VO（关联题目信息）
     */
    @Override
    public List<UserAnswerVO> convertToVOList(List<UserAnswer> userAnswers) {

        // 获取试卷ID（所有答案属于同一考试，取第一个即可）
        String examId = userAnswers.get(0).getExamId();
        // 查询考试对应的试卷详情（包含题目列表）
        Long paperId = baseMapper.selectPaperIdByExamId(examId); // 需要在UserAnswerMapper中添加该方法
        if (paperId == null) {
            throw new BusinessException("未找到考试对应的试卷");
        }
        List<QuestionVO> questions = paperFeignService.getPaperDetail(paperId).getQuestions();

        // 转换VO
        return userAnswers.stream().map(answer -> {
            UserAnswerVO vo = new UserAnswerVO();
            vo.setId(answer.getId());
            vo.setExamId(answer.getExamId());
            vo.setQuestionId(answer.getQuestionId());
            vo.setUserAnswer(answer.getUserAnswer());
            vo.setIsCorrect(answer.getIsCorrect());
            vo.setSubmitTime(answer.getSubmitTime());

            // 关联题目信息
            QuestionVO question = questions.stream()
                    .filter(q -> q.getQuestionId().equals(answer.getQuestionId()))
                    .findFirst()
                    .orElse(null);
            if (question != null) {
                vo.setQuestionContent(question.getQuestionContent());
                vo.setQuestionType(question.getQuestionType());
                vo.setQuestionTypeLabel(question.getQuestionTypeLabel());
                vo.setCorrectAnswer(question.getCorrectAnswer());
                vo.setScore(question.getScore());
            }
            return vo;
        }).collect(Collectors.toList());
    }
}