package com.dangshi.exam.exam.vo;

import lombok.Data;

import java.time.LocalDateTime;

// 嵌套VO：用户答案
@Data
public class UserAnswerVO {
    private Long questionId; // 题目ID
    private String userAnswer; // 用户答案

    public void setId(Long id) {
    }

    public void setExamId(String examId) {
    }

    public void setIsCorrect(Integer isCorrect) {
    }

    public void setSubmitTime(LocalDateTime submitTime) {
    }

    public void setQuestionContent(String questionContent) {
    }

    public void setQuestionType(Integer questionType) {
    }

    public void setQuestionTypeLabel(String questionTypeLabel) {
    }

    public void setCorrectAnswer(String correctAnswer) {
    }

    public void setScore(Integer score) {

    }
}
