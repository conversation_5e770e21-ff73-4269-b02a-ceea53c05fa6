package com.dangshi.exam.exam.vo;

import com.dangshi.exam.common.vo.QuestionVO;
import lombok.Data;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 考试结果视图对象（用于前端展示考试成绩和详情）
 */
@Data
public class ExamResultVO {
    /**
     * 考试ID（唯一标识）
     */
    private String examId;

    /**
     * 试卷名称
     */
    private String paperName;

    /**
     * 试卷总分
     */
    private Integer totalScore;

    /**
     * 用户得分
     */
    private Integer userScore;

    /**
     * 正确题数
     */
    private Integer correctCount;

    /**
     * 错误题数
     */
    private Integer wrongCount;

    /**
     * 考试开始时间
     */
    private LocalDateTime startTime;

    /**
     * 考试结束时间
     */
    private LocalDateTime endTime;

    /**
     * 考试时长（分钟）
     */
    private Integer timeUsed;

    /**
     * 错题列表（包含正确答案和解析）
     */
    private List<QuestionVO> wrongQuestions;

    /**
     * 用户答案详情列表（包含题目、用户答案、正确答案）
     */
    private List<UserAnswerDetailVO> userAnswers;

    public void setWrongQuestions(List<UserAnswerDetailVO> wrongAnswers) {
    }

    /**
     * 用户答案详情嵌套类
     */
    @Data
    public static class UserAnswerDetailVO {
        /**
         * 题目ID
         */
        private Long questionId;

        /**
         * 题目内容
         */
        private String questionContent;

        /**
         * 题型（1-单选，2-多选，3-判断）
         */
        private Integer questionType;

        /**
         * 题型文字描述
         */
        private String questionTypeLabel;

        /**
         * 用户答案
         */
        private String userAnswer;

        /**
         * 正确答案
         */
        private String correctAnswer;

        /**
         * 是否正确（0-错，1-对）
         */
        private Integer isCorrect;

        /**
         * 题目分值
         */
        private Integer score;

        /**
         * 答案解析
         */
        private String analysis;
    }
}