package com.dangshi.exam.exam.vo;

import lombok.Data;

/**
 * 考试结果统计VO（用于试卷维度的统计分析）
 */
@Data
public class ExamResultStatVO {
    /**
     * 试卷ID
     */
    private Long paperId;

    /**
     * 总考试次数
     */
    private Integer totalExam = 0;

    /**
     * 平均分
     */
    private Double avgScore = 0.0;

    /**
     * 通过率（%）
     */
    private Double passRate = 0.0;

    /**
     * 通过次数
     */
    private Integer passCount = 0;

    /**
     * 未通过次数
     */
    private Integer failCount = 0;
}