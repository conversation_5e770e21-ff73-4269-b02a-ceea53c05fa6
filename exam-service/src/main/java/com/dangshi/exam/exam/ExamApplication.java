package com.dangshi.exam.exam;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 考试微服务启动类
 */
@SpringBootApplication(scanBasePackages = "com.dangshi.exam")
@MapperScan("com.dangshi.exam.exam.mapper")
@EnableDiscoveryClient
@EnableFeignClients(basePackages = "com.dangshi.exam.exam.feign") // 启用Feign客户端
public class ExamApplication {
    public static void main(String[] args) {
        SpringApplication.run(ExamApplication.class, args);
    }
}