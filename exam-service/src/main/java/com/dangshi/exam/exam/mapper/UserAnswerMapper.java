package com.dangshi.exam.exam.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangshi.exam.exam.entity.UserAnswer;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户答案Mapper
 */
@Mapper
public interface UserAnswerMapper extends BaseMapper<UserAnswer> {
    /**
     * 批量插入用户答案
     */
    int batchInsert(@Param("list") List<UserAnswer> userAnswers);

    /**
     * 根据考试ID查询用户答案
     */
    List<UserAnswer> selectByExamId(@Param("examId") String examId);

    Long selectPaperIdByExamId(String examId);
}