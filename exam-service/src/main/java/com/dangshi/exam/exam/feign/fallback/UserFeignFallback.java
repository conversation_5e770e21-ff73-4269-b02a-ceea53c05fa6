package com.dangshi.exam.exam.feign.fallback;

import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.exam.feign.UserFeignService;
import com.dangshi.exam.user.entity.User;
import org.springframework.stereotype.Component;

@Component
public class UserFeignFallback implements UserFeignService {
    @Override
    public User getUserById(Long userId) {
        throw new BusinessException("用户服务暂时不可用，请稍后重试");
    }
}