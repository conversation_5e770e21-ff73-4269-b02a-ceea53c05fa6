package com.dangshi.exam.exam.feign.fallback;

import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.exam.feign.UserFeignService;
import com.dangshi.exam.common.vo.UserVO;
import org.springframework.stereotype.Component;

@Component
public class UserFeignFallback implements UserFeignService {
    @Override
    public UserVO getUserById(Long userId) {
        throw new BusinessException("用户服务暂时不可用，请稍后重试");
    }
}