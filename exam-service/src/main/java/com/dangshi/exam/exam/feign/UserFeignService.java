package com.dangshi.exam.exam.feign;

import com.dangshi.exam.exam.feign.fallback.UserFeignFallback;
import com.dangshi.exam.common.vo.UserVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 调用用户服务的Feign接口
 */
@FeignClient(value = "user-service", fallback = UserFeignFallback.class)
public interface UserFeignService {

    /**
     * 根据用户ID查询用户信息
     */
    @GetMapping("/feign/user/{userId}")
    UserVO getUserById(@PathVariable("userId") Long userId);
}