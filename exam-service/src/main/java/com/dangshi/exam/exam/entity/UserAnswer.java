package com.dangshi.exam.exam.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 用户答案表（t_user_answer）
 */
@Data
@TableName("t_user_answer")
public class UserAnswer {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键

    private String examId; // 考试ID（关联t_exam）

    private Long questionId; // 题目ID（关联t_question）

    private String userAnswer; // 用户答案

    private Integer isCorrect; // 是否正确：0-错，1-对

    private LocalDateTime submitTime; // 提交时间
}