<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.dangshi.exam.question.mapper.PaperQuestionMapper">
    <delete id="deleteByPaperId"></delete>

    <!-- 批量查询题目 -->
    <select id="selectBatchByIds" parameterType="java.util.List" resultType="com.dangshi.exam.question.entity.Question">
        SELECT
        question_id AS questionId,
        question_type AS questionType,
        question_content AS questionContent,
        options,
        correct_answer AS correctAnswer,
        score,
        analysis
        FROM t_question
        WHERE question_id IN
        <foreach collection="ids" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>
    <select id="selectQuestionIdsByPaperId" resultType="java.lang.Long">
        SELECT question_id FROM t_paper_question WHERE paper_id = #{paperId} ORDER BY sort
    </select>

</mapper>