package com.dangshi.exam.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.time.LocalDateTime;

/**
 * 试卷实体（t_paper）
 */
@Data
@TableName("t_paper")
public class Paper {
    @TableId(type = IdType.AUTO)
    private Long paperId; // 试卷ID

    private String paperName; // 试卷名称

    private Integer timeLimit; // 考试时长（分钟）

    private Integer totalScore; // 总分（自动计算）

    private LocalDateTime createTime; // 创建时间
}