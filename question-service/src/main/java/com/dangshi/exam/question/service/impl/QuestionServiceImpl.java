package com.dangshi.exam.question.service.impl;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.question.entity.Question;
import com.dangshi.exam.question.mapper.QuestionMapper;
import com.dangshi.exam.question.service.QuestionService;
import com.dangshi.exam.question.vo.QuestionVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 题目服务实现
 */
@Service
public class QuestionServiceImpl extends ServiceImpl<QuestionMapper, Question> implements QuestionService {

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 分页查询题目
     */
    @Override
    public IPage<QuestionVO> getQuestionPage(Integer pageNum, Integer pageSize) {
        Page<Question> page = new Page<>(pageNum, pageSize);
        IPage<Question> questionPage = questionMapper.selectPage(page, null);

        // 转换为VO
        return questionPage.convert(this::convertToVO);
    }

    /**
     * 创建题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> createQuestion(Question question) {
        validateQuestion(question);
        int rows = questionMapper.insert(question);
        if (rows <= 0) {
            throw new BusinessException("创建题目失败");
        }
        return R.success();
    }

    /**
     * 更新题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> updateQuestion(Question question) {
        if (question.getQuestionId() == null) {
            throw new BusinessException("题目ID不能为空");
        }
        validateQuestion(question);
        int rows = questionMapper.updateById(question);
        if (rows <= 0) {
            throw new BusinessException("更新题目失败");
        }
        return R.success();
    }

    /**
     * 删除题目
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> deleteQuestion(Long questionId) {
        int rows = questionMapper.deleteById(questionId);
        if (rows <= 0) {
            throw new BusinessException("删除题目失败");
        }
        return R.success();
    }

    /**
     * 批量查询题目（转换为VO）
     */
    @Override
    public List<QuestionVO> getQuestionVOs(List<Long> questionIds) {
        if (questionIds == null || questionIds.isEmpty()) {
            return List.of();
        }
        List<Question> questions = questionMapper.selectBatchByIds(questionIds);
        return questions.stream().map(this::convertToVO).collect(Collectors.toList());
    }

    /**
     * 题目校验
     */
    private void validateQuestion(Question question) {
        if (question.getQuestionType() == null || (question.getQuestionType() < 1 || question.getQuestionType() > 3)) {
            throw new BusinessException("题型必须为1（单选）、2（多选）或3（判断）");
        }
        if (question.getQuestionContent() == null || question.getQuestionContent().trim().isEmpty()) {
            throw new BusinessException("题目内容不能为空");
        }
        if (question.getScore() == null || question.getScore() <= 0) {
            throw new BusinessException("分值必须大于0");
        }
        if (question.getCorrectAnswer() == null || question.getCorrectAnswer().trim().isEmpty()) {
            throw new BusinessException("正确答案不能为空");
        }
    }

    /**
     * 实体转VO
     */
    private QuestionVO convertToVO(Question question) {
        QuestionVO vo = new QuestionVO();
        vo.setQuestionId(question.getQuestionId());
        vo.setQuestionType(question.getQuestionType());
        vo.setQuestionContent(question.getQuestionContent());
        vo.setCorrectAnswer(question.getCorrectAnswer());
        vo.setScore(question.getScore());
        vo.setAnalysis(question.getAnalysis());

        // 设置题型标签
        switch (question.getQuestionType()) {
            case 1: vo.setQuestionTypeLabel("单选"); break;
            case 2: vo.setQuestionTypeLabel("多选"); break;
            case 3: vo.setQuestionTypeLabel("判断"); break;
            default: vo.setQuestionTypeLabel("未知");
        }

        // 解析选项JSON
        if (question.getOptions() != null && !question.getOptions().isEmpty()) {
            try {
                vo.setOptions(objectMapper.readValue(question.getOptions(), String[].class));
            } catch (JsonProcessingException e) {
                throw new BusinessException("选项格式错误");
            }
        }
        return vo;
    }
}