package com.dangshi.exam.question.feign;

import com.dangshi.exam.question.feign.fallback.PaperFeignFallback;
import com.dangshi.exam.question.vo.PaperVO;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

/**
 * 试卷Feign接口（供exam-service调用）
 */
@FeignClient(value = "question-service", fallback = PaperFeignFallback.class)
public interface PaperFeignService {

    /**
     * 查询试卷详情（含题目列表）
     */
    @GetMapping("/feign/paper/{paperId}")
    PaperVO getPaperDetail(@PathVariable("paperId") Long paperId);
}