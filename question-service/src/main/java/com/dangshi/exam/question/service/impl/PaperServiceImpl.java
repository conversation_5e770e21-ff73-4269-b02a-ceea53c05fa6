package com.dangshi.exam.question.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.common.utils.RedisUtils;
import com.dangshi.exam.question.entity.Paper;
import com.dangshi.exam.question.entity.PaperQuestion;
import com.dangshi.exam.question.entity.Question;
import com.dangshi.exam.question.mapper.PaperMapper;
import com.dangshi.exam.question.mapper.PaperQuestionMapper;
import com.dangshi.exam.question.mapper.QuestionMapper;
import com.dangshi.exam.question.service.PaperService;
import com.dangshi.exam.question.service.QuestionService;
import com.dangshi.exam.question.vo.PaperVO;
import com.dangshi.exam.question.vo.QuestionVO;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 试卷服务实现（含Redis缓存逻辑）
 */
@Service
public class PaperServiceImpl extends ServiceImpl<PaperMapper, Paper> implements PaperService {

    // Redis缓存键前缀（试卷详情）
    private static final String PAPER_DETAIL_KEY = "paper:detail:";

    // 缓存过期时间（分钟）
    private static final int CACHE_EXPIRE_MINUTES = 30;

    @Resource
    private PaperMapper paperMapper;

    @Resource
    private PaperQuestionMapper paperQuestionMapper;

    @Resource
    private QuestionMapper questionMapper;

    @Resource
    private QuestionService questionService;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private ObjectMapper objectMapper;

    /**
     * 创建试卷（含题目关联）
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> createPaper(PaperVO paperVO) {
        // 1. 校验参数
        validatePaperVO(paperVO);

        // 2. 计算总分（题目分值之和）
        List<Question> questions = questionMapper.selectBatchByIds(paperVO.getQuestionIds());
        int totalScore = questions.stream().mapToInt(Question::getScore).sum();

        // 3. 保存试卷基本信息
        Paper paper = new Paper();
        paper.setPaperName(paperVO.getPaperName());
        paper.setTimeLimit(paperVO.getTimeLimit());
        paper.setTotalScore(totalScore);
        paper.setCreateTime(LocalDateTime.now());
        paperMapper.insert(paper);

        // 4. 保存试卷-题目关联
        savePaperQuestions(paper.getPaperId(), paperVO.getQuestionIds());

        return R.success();
    }

    /**
     * 更新试卷
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> updatePaper(PaperVO paperVO) {
        if (paperVO.getPaperId() == null) {
            throw new BusinessException("试卷ID不能为空");
        }

        // 1. 校验参数
        validatePaperVO(paperVO);

        // 2. 计算总分
        List<Question> questions = questionMapper.selectBatchByIds(paperVO.getQuestionIds());
        int totalScore = questions.stream().mapToInt(Question::getScore).sum();

        // 3. 更新试卷基本信息
        Paper paper = new Paper();
        paper.setPaperId(paperVO.getPaperId());
        paper.setPaperName(paperVO.getPaperName());
        paper.setTimeLimit(paperVO.getTimeLimit());
        paper.setTotalScore(totalScore);
        paperMapper.updateById(paper);

        // 4. 先删除旧关联，再保存新关联
        paperQuestionMapper.deleteByPaperId(paperVO.getPaperId());
        savePaperQuestions(paperVO.getPaperId(), paperVO.getQuestionIds());

        // 5. 清除缓存（更新后缓存失效）
        redisUtils.delete(PAPER_DETAIL_KEY + paperVO.getPaperId());

        return R.success();
    }

    /**
     * 删除试卷
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public R<?> deletePaper(Long paperId) {
        // 1. 删除试卷-题目关联
        paperQuestionMapper.deleteByPaperId(paperId);

        // 2. 删除试卷
        int rows = paperMapper.deleteById(paperId);
        if (rows <= 0) {
            throw new BusinessException("删除试卷失败");
        }

        // 3. 清除缓存
        redisUtils.delete(PAPER_DETAIL_KEY + paperId);

        return R.success();
    }

    /**
     * 查询试卷详情（Redis缓存）
     */
    @Override
    public R<PaperVO> getPaperDetail(Long paperId) {
        // 1. 先查缓存
        String cacheKey = PAPER_DETAIL_KEY + paperId;
        Object cacheObj = redisUtils.get(cacheKey);
        if (cacheObj != null) {
            try {
                PaperVO paperVO = objectMapper.readValue(cacheObj.toString(), PaperVO.class);
                return R.success(paperVO);
            } catch (JsonProcessingException e) {
                // 缓存格式错误，删除缓存
                redisUtils.delete(cacheKey);
            }
        }

        // 2. 缓存未命中，查数据库
        PaperVO paperVO = getPaperDetailFromDb(paperId);

        // 3. 存入缓存（设置过期时间）
        try {
            redisUtils.set(cacheKey, objectMapper.writeValueAsString(paperVO), CACHE_EXPIRE_MINUTES, java.util.concurrent.TimeUnit.MINUTES);
        } catch (JsonProcessingException e) {
        }

        return R.success(paperVO);
    }

    /**
     * 查询试卷列表
     */
    @Override
    public R<List<Paper>> getPaperList() {
        List<Paper> papers = paperMapper.selectList(new QueryWrapper<Paper>().orderByDesc("create_time"));
        return R.success(papers);
    }

    /**
     * 供Feign调用：查询试卷详情
     */
    @Override
    public PaperVO getPaperDetailForFeign(Long paperId) {
        return getPaperDetailFromDb(paperId);
    }

    /**
     * 从数据库查询试卷详情
     */
    private PaperVO getPaperDetailFromDb(Long paperId) {
        // 1. 查询试卷基本信息
        Paper paper = paperMapper.selectById(paperId);
        if (paper == null) {
            throw new BusinessException("试卷不存在");
        }

        // 2. 查询关联的题目ID
        List<Long> questionIds = paperQuestionMapper.selectQuestionIdsByPaperId(paperId);

        // 3. 查询题目详情
        List<QuestionVO> questions = questionService.getQuestionVOs(questionIds);

        // 4. 封装VO
        PaperVO paperVO = new PaperVO();
        paperVO.setPaperId(paper.getPaperId());
        paperVO.setPaperName(paper.getPaperName());
        paperVO.setTimeLimit(paper.getTimeLimit());
        paperVO.setTotalScore(paper.getTotalScore());
        paperVO.setCreateTime(paper.getCreateTime());
        paperVO.setQuestionIds(questionIds);
        paperVO.setQuestions(questions);

        return paperVO;
    }

    /**
     * 保存试卷-题目关联
     */
    private void savePaperQuestions(Long paperId, List<Long> questionIds) {
        for (int i = 0; i < questionIds.size(); i++) {
            PaperQuestion paperQuestion = new PaperQuestion();
            paperQuestion.setPaperId(paperId);
            paperQuestion.setQuestionId(questionIds.get(i));
            paperQuestion.setSort(i + 1); // 排序从1开始
            paperQuestionMapper.insert(paperQuestion);
        }
    }

    /**
     * 校验试卷VO
     */
    private void validatePaperVO(PaperVO paperVO) {
        if (paperVO.getPaperName() == null || paperVO.getPaperName().trim().isEmpty()) {
            throw new BusinessException("试卷名称不能为空");
        }
        if (paperVO.getTimeLimit() == null || paperVO.getTimeLimit() < 5) {
            throw new BusinessException("考试时长不能少于5分钟");
        }
        if (paperVO.getQuestionIds() == null || paperVO.getQuestionIds().isEmpty()) {
            throw new BusinessException("请至少选择一道题目");
        }
    }
}