package com.dangshi.exam.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangshi.exam.question.entity.Question;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 题目Mapper
 */
@Mapper
public interface QuestionMapper extends BaseMapper<Question> {
    /**
     * 批量查询题目
     */
    List<Question> selectBatchByIds(@Param("ids") List<Long> questionIds);
}