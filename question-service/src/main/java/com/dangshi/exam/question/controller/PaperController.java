package com.dangshi.exam.question.controller;

import com.dangshi.exam.common.result.R;
import com.dangshi.exam.question.entity.Paper;
import com.dangshi.exam.question.service.PaperService;
import com.dangshi.exam.question.vo.PaperVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

/**
 * 试卷管理接口
 */
@RestController
@RequestMapping("/api/paper")
public class PaperController {

    @Resource
    private PaperService paperService;

    /**
     * 创建试卷
     */
    @PostMapping("/create")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> createPaper(@RequestBody PaperVO paperVO) {
        return paperService.createPaper(paperVO);
    }

    /**
     * 更新试卷
     */
    @PutMapping("/update")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> updatePaper(@RequestBody PaperVO paperVO) {
        return paperService.updatePaper(paperVO);
    }

    /**
     * 删除试卷
     */
    @DeleteMapping("/{paperId}")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> deletePaper(@PathVariable Long paperId) {
        return paperService.deletePaper(paperId);
    }

    /**
     * 查询试卷详情（公开接口，带Redis缓存）
     */
    @GetMapping("/{paperId}")
    public R<PaperVO> getPaperDetail(@PathVariable Long paperId) {
        return paperService.getPaperDetail(paperId);
    }

    /**
     * 查询试卷列表（公开接口）
     */
    @GetMapping("/list")
    public R<List<Paper>> getPaperList() {
        return paperService.getPaperList();
    }
}