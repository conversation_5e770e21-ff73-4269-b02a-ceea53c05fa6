package com.dangshi.exam.question;

import org.mybatis.spring.annotation.MapperScan;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.client.discovery.EnableDiscoveryClient;
import org.springframework.cloud.openfeign.EnableFeignClients;

/**
 * 题库微服务启动类
 */
@SpringBootApplication(scanBasePackages = {"com.dangshi.exam.question", "com.dangshi.exam.common"})
@MapperScan("com.dangshi.exam.question.mapper")
@EnableDiscoveryClient
@EnableFeignClients // 启用Feign（可能调用用户服务）
public class QuestionApplication {
    public static void main(String[] args) {
        SpringApplication.run(QuestionApplication.class, args);
    }
}