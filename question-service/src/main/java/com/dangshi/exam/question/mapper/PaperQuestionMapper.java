package com.dangshi.exam.question.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.dangshi.exam.question.entity.PaperQuestion;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 试卷-题目关联Mapper
 */
@Mapper
public interface PaperQuestionMapper extends BaseMapper<PaperQuestion> {
    /**
     * 根据试卷ID查询关联的题目ID（按排序）
     */
    List<Long> selectQuestionIdsByPaperId(@Param("paperId") Long paperId);

    /**
     * 根据试卷ID删除所有关联
     */
    int deleteByPaperId(@Param("paperId") Long paperId);
}