package com.dangshi.exam.question.feign.fallback;

import com.dangshi.exam.common.exception.BusinessException;
import com.dangshi.exam.question.feign.PaperFeignService;
import com.dangshi.exam.question.vo.PaperVO;
import org.springframework.stereotype.Component;

/**
 * 试卷Feign降级处理
 */
@Component
public class PaperFeignFallback implements PaperFeignService {

    @Override
    public PaperVO getPaperDetail(Long paperId) {
        throw new BusinessException("题库服务暂时不可用，请稍后重试");
    }
}