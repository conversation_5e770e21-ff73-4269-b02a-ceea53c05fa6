package com.dangshi.exam.question.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.question.entity.Paper;
import com.dangshi.exam.question.vo.PaperVO;

import java.util.List;

/**
 * 试卷服务接口
 */
public interface PaperService extends IService<Paper> {
    /**
     * 创建试卷（含题目关联）
     */
    R<?> createPaper(PaperVO paperVO);

    /**
     * 更新试卷
     */
    R<?> updatePaper(PaperVO paperVO);

    /**
     * 删除试卷
     */
    R<?> deletePaper(Long paperId);

    /**
     * 查询试卷详情（含题目列表，Redis缓存）
     */
    R<PaperVO> getPaperDetail(Long paperId);

    /**
     * 查询试卷列表
     */
    R<List<Paper>> getPaperList();

    /**
     * 供Feign调用：查询试卷详情（无权限校验）
     */
    PaperVO getPaperDetailForFeign(Long paperId);
}