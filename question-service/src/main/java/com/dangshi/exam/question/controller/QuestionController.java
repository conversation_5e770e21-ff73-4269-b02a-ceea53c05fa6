package com.dangshi.exam.question.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.dangshi.exam.common.result.R;
import com.dangshi.exam.question.entity.Question;
import com.dangshi.exam.question.service.QuestionService;
import com.dangshi.exam.question.vo.QuestionVO;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 题目管理接口（需管理员权限）
 */
@RestController
@RequestMapping("/api/question")
public class QuestionController {

    @Resource
    private QuestionService questionService;

    /**
     * 分页查询题目
     */
    @GetMapping("/page")
    @PreAuthorize("hasRole('ADMIN')")
    public R<IPage<QuestionVO>> getQuestionPage(
            @RequestParam(defaultValue = "1") Integer pageNum,
            @RequestParam(defaultValue = "10") Integer pageSize
    ) {
        return R.success(questionService.getQuestionPage(pageNum, pageSize));
    }

    /**
     * 创建题目
     */
    @PostMapping("/create")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> createQuestion(@RequestBody Question question) {
        return questionService.createQuestion(question);
    }

    /**
     * 更新题目
     */
    @PutMapping("/update")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> updateQuestion(@RequestBody Question question) {
        return questionService.updateQuestion(question);
    }

    /**
     * 删除题目
     */
    @DeleteMapping("/{questionId}")
    @PreAuthorize("hasRole('ADMIN')")
    public R<?> deleteQuestion(@PathVariable Long questionId) {
        return questionService.deleteQuestion(questionId);
    }
}