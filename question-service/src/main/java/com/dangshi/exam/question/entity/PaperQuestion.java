package com.dangshi.exam.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 试卷-题目关联表（t_paper_question）
 */
@Data
@TableName("t_paper_question")
public class PaperQuestion {
    @TableId(type = IdType.AUTO)
    private Long id; // 主键

    private Long paperId; // 试卷ID

    private Long questionId; // 题目ID

    private Integer sort; // 题目排序
}