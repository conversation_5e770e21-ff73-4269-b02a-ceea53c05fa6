package com.dangshi.exam.question.controller;

import com.dangshi.exam.question.service.PaperService;
import com.dangshi.exam.question.vo.PaperVO;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 试卷Feign接口实现
 */
@RestController
@RequestMapping("/feign/paper")
public class FeignController {

    @Resource
    private PaperService paperService;

    /**
     * 查询试卷详情（供Feign调用）
     */
    @GetMapping("/{paperId}")
    public PaperVO getPaperDetail(@PathVariable("paperId") Long paperId) {
        return paperService.getPaperDetailForFeign(paperId);
    }
}