package com.dangshi.exam.question.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

/**
 * 题目实体（t_question）
 */
@Data
@TableName("t_question")
public class Question {
    @TableId(type = IdType.AUTO)
    private Long questionId; // 题目ID

    private Integer questionType; // 题型：1-单选，2-多选，3-判断

    private String questionContent; // 题目内容

    private String options; // 选项（JSON格式，如["A.xxx","B.xxx"]）

    private String correctAnswer; // 正确答案

    private Integer score; // 分值

    private String analysis; // 解析（可选）
}