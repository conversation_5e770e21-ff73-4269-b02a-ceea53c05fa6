# 服务端口
server:
  port: 8080

# 服务名称（Nacos注册名）
spring:
  application:
    name: gateway-service
  main:
    web-application-type: reactive
  # Nacos服务发现配置
  cloud:
    nacos:
      discovery:
        server-addr: localhost:8848 # Nacos地址
        namespace: public # Nacos命名空间（默认public）
    # Gateway配置
    gateway:
      discovery:
        locator:
          enabled: true # 启用服务发现路由（可通过服务名直接访问）
      httpclient:
        connect-timeout: 5000 # 连接超时时间
        response-timeout: 5000 # 响应超时时间
      routes:
        - id: user-service-route
          uri: lb://user-service
          predicates:
            - Path=/api/user/**
          filters:
            - StripPrefix=0  # 不删除前缀

        - id: question-service-route
          uri: lb://question-service
          predicates:
            - Path=/api/paper/**,/api/question/**,/feign/paper/**
          filters:
            - StripPrefix=0

  # Redis配置
  data:
    redis:
      host: localhost # Redis地址
      port: 6379 # Redis端口
      password: # Redis密码（无则留空）
      database: 0 # Redis数据库索引
      timeout: 2000 # 超时时间

# JWT配置（与其他微服务一致）
dangshi:
  exam:
    jwt:
      secret: dangshi-exam-secret-key-2024 # 密钥（生产环境需更换）
      expiration: 86400000 # 过期时间（毫秒，24小时）
