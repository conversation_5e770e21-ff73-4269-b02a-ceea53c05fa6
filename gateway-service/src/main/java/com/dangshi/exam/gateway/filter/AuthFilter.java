package com.dangshi.exam.gateway.filter;

import com.dangshi.exam.common.result.R;
import com.dangshi.exam.common.security.JwtUtils;
import com.dangshi.exam.common.utils.RedisUtils;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.List;

/**
 * 网关全局过滤器：Token校验（除白名单外的请求需携带有效Token）
 */
@Component
@Slf4j
public class AuthFilter implements GlobalFilter, Ordered {

    // 白名单路径（无需Token的接口）
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/api/user/login",
            "/api/user/register"
    );

    // 路径匹配器（用于判断请求路径是否在白名单）
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    @Resource
    private JwtUtils jwtUtils;

    @Resource
    private RedisUtils redisUtils;

    @Resource
    private ObjectMapper objectMapper;

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // 1. 获取请求路径
        String path = request.getPath().value();
        log.info("网关拦截请求：{}", path);

        // 2. 白名单路径直接放行
        for (String whitePath : WHITE_LIST) {
            if (pathMatcher.match(whitePath, path)) {
                return chain.filter(exchange);
            }
        }

        // 3. 非白名单路径：获取Token
        String token = request.getHeaders().getFirst("Authorization");
        if (StringUtils.isEmpty(token) || !token.startsWith("Bearer ")) {
            // Token不存在或格式错误，返回401
            return handleUnauthorized(response, "请先登录");
        }

        // 4. 解析Token（去掉"Bearer "前缀）
        token = token.substring(7);
        String username;
        try {
            username = jwtUtils.extractUsername(token);
        } catch (Exception e) {
            log.error("Token解析失败：{}", e.getMessage());
            return handleUnauthorized(response, "Token无效");
        }

        // 5. 检查Token是否过期
        if (jwtUtils.isTokenExpired(token)) {
            return handleUnauthorized(response, "Token已过期，请重新登录");
        }

        // 6. 检查Token是否在黑名单（如用户退出登录后失效的Token）
        String blacklistKey = "token:blacklist:" + token;
        if (Boolean.TRUE.equals(redisUtils.hasKey(blacklistKey))) {
            return handleUnauthorized(response, "Token已失效，请重新登录");
        }

        // 7. Token有效：将用户名存入请求头，供下游服务使用
        ServerHttpRequest newRequest = request.mutate()
                .header("X-Username", username)
                .build();
        exchange.mutate().request(newRequest).build();

        // 8. 放行请求
        return chain.filter(exchange);
    }

    /**
     * 处理未授权请求（返回401+统一响应格式）
     */
    private Mono<Void> handleUnauthorized(ServerHttpResponse response, String message) {
        // 设置响应状态码401
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        // 设置响应格式为JSON
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        // 构建统一响应结果
        R<?> result = R.error(R.UNAUTHORIZED, message);
        String json;
        try {
            json = objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败：{}", e.getMessage());
            json = "{\"code\":500,\"message\":\"系统异常\",\"data\":null}";
        }

        // 写入响应
        return response.writeWith(Mono.just(response.bufferFactory().wrap(json.getBytes())));
    }

    /**
     * 过滤器优先级：数值越小，优先级越高
     */
    @Override
    public int getOrder() {
        return -100;
    }
}