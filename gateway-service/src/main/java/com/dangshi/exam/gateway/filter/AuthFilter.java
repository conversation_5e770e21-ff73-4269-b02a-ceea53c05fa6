package com.dangshi.exam.gateway.filter;

import com.dangshi.exam.gateway.result.R;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.cloud.gateway.filter.GatewayFilterChain;
import org.springframework.cloud.gateway.filter.GlobalFilter;
import org.springframework.core.Ordered;
import org.springframework.http.HttpStatus;
import org.springframework.http.server.reactive.ServerHttpRequest;
import org.springframework.http.server.reactive.ServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.util.AntPathMatcher;
import org.springframework.util.StringUtils;
import org.springframework.web.server.ServerWebExchange;
import reactor.core.publisher.Mono;
import java.util.Arrays;
import java.util.List;

/**
 * 网关全局过滤器：Token校验（除白名单外的请求需携带有效Token）
 */
@Component
@Slf4j
public class AuthFilter implements GlobalFilter, Ordered {

    // 白名单路径（无需Token的接口）
    private static final List<String> WHITE_LIST = Arrays.asList(
            "/api/user/login",
            "/api/user/register"
    );

    // 路径匹配器（用于判断请求路径是否在白名单）
    private final AntPathMatcher pathMatcher = new AntPathMatcher();

    // 暂时移除JWT依赖，简化验证逻辑
    // private JwtUtils jwtUtils;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Override
    public Mono<Void> filter(ServerWebExchange exchange, GatewayFilterChain chain) {
        ServerHttpRequest request = exchange.getRequest();
        ServerHttpResponse response = exchange.getResponse();

        // 1. 获取请求路径
        String path = request.getPath().value();
        log.info("网关拦截请求：{}", path);

        // 2. 白名单路径直接放行
        for (String whitePath : WHITE_LIST) {
            if (pathMatcher.match(whitePath, path)) {
                return chain.filter(exchange);
            }
        }

        // 3. 非白名单路径：获取Token
        String token = request.getHeaders().getFirst("Authorization");
        if (StringUtils.isEmpty(token) || !token.startsWith("Bearer ")) {
            // Token不存在或格式错误，返回401
            return handleUnauthorized(response, "请先登录");
        }

        // 4. 简化验证：暂时跳过JWT验证（生产环境需要完整验证）
        log.debug("跳过JWT验证（JWT工具类未配置）");

        // 5. 模拟从Token中提取用户名（实际应该从JWT中解析）
        String username = "test-user"; // 临时硬编码，生产环境需要从JWT解析

        // 6. 检查Token是否在黑名单（暂时跳过Redis检查）
        // 注意：生产环境中应该使用Redis来维护Token黑名单
        log.debug("跳过Redis黑名单检查（Redis未配置）");

        // 7. Token有效：将用户名存入请求头，供下游服务使用
        ServerHttpRequest newRequest = request.mutate()
                .header("X-Username", username)
                .build();
        ServerWebExchange newExchange = exchange.mutate().request(newRequest).build();

        // 8. 放行请求
        return chain.filter(newExchange);
    }

    /**
     * 处理未授权请求（返回401+统一响应格式）
     */
    private Mono<Void> handleUnauthorized(ServerHttpResponse response, String message) {
        // 设置响应状态码401
        response.setStatusCode(HttpStatus.UNAUTHORIZED);
        // 设置响应格式为JSON
        response.getHeaders().add("Content-Type", "application/json;charset=UTF-8");

        // 构建统一响应结果
        R<?> result = R.error(401, message);
        String json;
        try {
            json = objectMapper.writeValueAsString(result);
        } catch (JsonProcessingException e) {
            log.error("JSON序列化失败：{}", e.getMessage());
            json = "{\"code\":500,\"message\":\"系统异常\",\"data\":null}";
        }

        // 写入响应
        return response.writeWith(Mono.just(response.bufferFactory().wrap(json.getBytes())));
    }

    /**
     * 过滤器优先级：数值越小，优先级越高
     */
    @Override
    public int getOrder() {
        return -100;
    }
}