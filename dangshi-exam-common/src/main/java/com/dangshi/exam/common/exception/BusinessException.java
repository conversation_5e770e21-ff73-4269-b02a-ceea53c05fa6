package com.dangshi.exam.common.exception;

import com.dangshi.exam.common.result.R;
import lombok.Data;

/**
 * 业务异常（用于处理已知的业务错误）
 */
@Data
public class BusinessException extends RuntimeException {
    private Integer code; // 错误码

    public BusinessException(String message) {
        super(message);
        this.code = R.ERROR; // 默认500
    }

    public BusinessException(Integer code, String message) {
        super(message);
        this.code = code;
    }
}