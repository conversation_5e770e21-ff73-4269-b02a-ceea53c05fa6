package com.dangshi.exam.common.vo;

import lombok.Data;
import java.time.LocalDateTime;
import java.util.List;

/**
 * 试卷信息VO（跨服务传输）
 */
@Data
public class PaperVO {
    /**
     * 试卷ID
     */
    private Long paperId;

    /**
     * 试卷名称
     */
    private String paperName;

    /**
     * 考试时长（分钟）
     */
    private Integer timeLimit;

    /**
     * 试卷总分
     */
    private Integer totalScore;

    /**
     * 试卷创建时间
     */
    private LocalDateTime createTime;

    /**
     * 题目ID列表（用于创建/编辑试卷时传递）
     */
    private List<Long> questionIds;

    /**
     * 题目列表（用于展示试卷内容，包含完整题目信息）
     */
    private List<QuestionVO> questions;

    /**
     * 题目数量
     */
    private Integer questionCount;

    /**
     * 单选题数量
     */
    private Integer singleChoiceCount;

    /**
     * 多选题数量
     */
    private Integer multipleChoiceCount;

    /**
     * 判断题数量
     */
    private Integer judgmentCount;

    // 补充计算题目数量的方法（可选）
    public void setQuestions(List<QuestionVO> questions) {
        this.questions = questions;
        if (questions == null) {
            return;
        }
        this.questionCount = questions.size();
        // 统计各题型数量
        this.singleChoiceCount = (int) questions.stream()
                .filter(q -> q.getQuestionType() == 1)
                .count();
        this.multipleChoiceCount = (int) questions.stream()
                .filter(q -> q.getQuestionType() == 2)
                .count();
        this.judgmentCount = (int) questions.stream()
                .filter(q -> q.getQuestionType() == 3)
                .count();
    }
}
