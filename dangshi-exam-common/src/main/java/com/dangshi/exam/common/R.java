package com.dangshi.exam.common;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 统一响应结果
 */
@Data
@NoArgsConstructor
@AllArgsConstructor
public class R<T> {
    // 状态码：200成功，500失败，401未授权，403权限不足
    public static final Integer SUCCESS = 200;
    public static final Integer ERROR = 500;
    public static final Integer UNAUTHORIZED = 401;
    public static final Integer FORBIDDEN = 403;

    private Integer code;
    private String message;
    private T data;

    // 成功（无数据）
    public static <T> R<T> success() {
        return new R<>(SUCCESS, "操作成功", null);
    }

    // 成功（有数据）
    public static <T> R<T> success(T data) {
        return new R<>(SUCCESS, "操作成功", data);
    }

    // 失败（自定义消息）
    public static <T> R<T> error(String message) {
        return new R<>(ERROR, message, null);
    }

    // 失败（自定义状态码+消息）
    public static <T> R<T> error(Integer code, String message) {
        return new R<>(code, message, null);
    }
}